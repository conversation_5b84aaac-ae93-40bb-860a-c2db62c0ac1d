# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

**Package Manager**: This project uses **pnpm** as the package manager.

**Primary development workflow:**
- `pnpm run dev` - Start Remix dev server
- `pnpm run build` - Build for production
- `pnpm run start` - Start production server locally (after build)
- `pnpm run deploy` - Deploy to Vercel

**Code quality:**
- `pnpm run check` - Run all Biome checks (lint + format)
- `pnpm run check:fix` - Fix all auto-fixable issues
- `pnpm run lint` / `pnpm run lint:fix` - Biome linting
- `pnpm run format` / `pnpm run format:fix` - Biome formatting
- `pnpm run typecheck` - TypeScript type checking
- `pnpm run test` - Run Vitest tests

**Database (Drizzle + Neon):**
- `pnpm run db:generate` - Generate migration files
- `pnpm run db:push` - Push schema changes (development)
- `pnpm run db:migrate` - Run migrations
- `pnpm run db:studio` - Open Drizzle Studio GUI

**Environment setup:**
- Copy `.env.example` to `.env` for local environment variables
- DATABASE_URL required for Neon PostgreSQL connection
- API keys (OpenAI, Stripe, etc.) go in `.env` for local, Vercel dashboard for production

## Architecture Overview

**Tech Stack:**
- **Runtime**: Remix on Vercel
- **Database**: Neon PostgreSQL with Drizzle ORM
- **Storage**: Vercel Blob Storage
- **State**: Zustand stores with TypeScript
- **Styling**: Tailwind CSS + Radix UI components
- **Analytics**: Google Analytics 4 integration
- **AI**: Multi-provider support (OpenAI, DeepSeek, OpenRouter, etc.)
- **Payments**: Stripe integration
- **i18n**: i18next with 6 languages (en, zh, es, fr, de, ja)

**Key Patterns:**

1. **Database**: Use `createDb(databaseUrl)` factory pattern with Drizzle schema in `app/lib/schema.ts`

2. **State Management**: Zustand stores with selectors and actions:
   - `useUserStore` - Authentication/user data
   - `useUIStore` - Theme, language, notifications
   - `useCartStore` - Shopping cart
   - `useAppStore` - Global app state

3. **i18n**: Language detection via cookies/URL params, resources in `app/lib/i18n/locales/`

4. **Components**: 
   - UI components in `app/components/ui/` (shadcn-style)
   - Layout components in `app/components/layout/`
   - Page blocks in `app/components/blocks/`

5. **Routes**: 
   - API routes prefixed with `api.`
   - Page routes follow Remix file-based routing
   - Landing page uses configurable blocks from `app/lib/landing-config.ts`

6. **Styling**: 
   - Theme system with CSS custom properties
   - Dark/light mode via Zustand store
   - Responsive design patterns

**Environment Variables:**
- Local: `.dev.vars` file
- Production: `wrangler.toml` vars section
- Required: DATABASE_URL, STRIPE keys, GA_TRACKING_ID
- Optional: AI provider keys, R2 storage config

**Testing:**
- Vitest for unit tests
- Test database connection via `/test-db` route
- Performance testing via `/performance` route