// app/lib/storage/image-storage.server.ts

import { put, del, list, type PutBlobResult, type ListBlobResult } from "@vercel/blob";

// Define a constant for the image storage path prefix
const IMAGE_PATH_PREFIX = "images/generated/";

export interface ImageMetadata {
  originalName?: string;
  contentType?: string;
  size?: number;
  uploaderId?: string; // Optional: if you track which user uploaded it
  [key: string]: string | number | undefined; // Allow other string/number properties
}

export class ImageStorageService {
  constructor() {
    // No constructor parameters needed for Vercel Blob
  }

  /**
   * Generates a unique key for an image.
   * @param filename - The original filename of the image.
   * @returns A unique key for storing the image in Vercel Blob.
   */
  private generateImageKey(filename: string): string {
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    // Ensure filename is a string and split correctly
    const nameParts = typeof filename === "string" ? filename.split(".") : [];
    const extension = nameParts.length > 1 ? nameParts.pop() : "png"; // Default to png if no extension or not a string
    return `${IMAGE_PATH_PREFIX}${timestamp}_${randomSuffix}.${extension}`;
  }

  /**
   * Uploads an image to Vercel Blob.
   * @param imageFile - The image file (e.g., from a FormData).
   * @param metadata - Metadata associated with the image.
   * @returns The Blob object details or null if upload fails.
   */
  async uploadImage(
    imageFile: File,
    _metadata: Partial<ImageMetadata> = {}
  ): Promise<PutBlobResult | null> {
    const key = this.generateImageKey(imageFile.name);

    try {
      const blob = await put(key, imageFile, {
        access: "public",
        addRandomSuffix: false,
      });

      return blob;
    } catch (error) {
      console.error(`Error uploading image ${key}:`, error);
      return null;
    }
  }

  /**
   * Retrieves an image URL from Vercel Blob.
   * @param key - The blob key.
   * @returns The image URL or null if not found.
   */
  async getImageUrl(key: string): Promise<string | null> {
    try {
      // For Vercel Blob, we can construct the URL directly
      // The URL format is typically: https://[blob-store-id].public.blob.vercel-storage.com/[key]
      // But we'll use the list API to get the actual URL
      const { blobs } = await list({ prefix: key, limit: 1 });
      if (blobs.length > 0 && blobs[0].pathname === key) {
        return blobs[0].url;
      }
      return null;
    } catch (error) {
      console.error(`Error retrieving image ${key}:`, error);
      return null;
    }
  }

  /**
   * Get a direct download URL for an image.
   * @param key - The blob key.
   * @returns The download URL.
   */
  getDownloadUrl(key: string): string {
    // For Vercel Blob, we can use the same URL for download
    return `/api/image-download?key=${encodeURIComponent(key)}`;
  }

  /**
   * Deletes an image from Vercel Blob.
   * @param key - The blob key.
   * @returns True if deletion was successful, false otherwise.
   */
  async deleteImage(key: string): Promise<boolean> {
    try {
      await del(key);
      return true;
    } catch (error) {
      console.error(`Error deleting image ${key}:`, error);
      return false;
    }
  }

  /**
   * Lists images in the storage.
   * @param options - List options (e.g., prefix, limit).
   * @returns A list of blob objects.
   */
  async listImages(options?: { prefix?: string; limit?: number }): Promise<ListBlobResult> {
    const listOptions = {
      prefix: options?.prefix || IMAGE_PATH_PREFIX,
      limit: options?.limit || 100,
    };
    return list(listOptions);
  }
}

/**
 * Create a new ImageStorageService instance
 */
export function createImageStorageService(): ImageStorageService {
  return new ImageStorageService();
}
