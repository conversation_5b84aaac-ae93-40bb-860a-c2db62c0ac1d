import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { Image, Loader2, MessageSquare, Sparkles, Zap } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Textarea } from "~/components/ui/textarea";
import { type AIProvider, getAvailableProviders } from "~/lib/ai/ai-providers";
import { createDbFromEnv } from "~/lib/db";
import { getUserCredits, getUserUuid } from "~/services/user-management.server";

export const meta: MetaFunction = () => {
  return [
    { title: "AI Tools - Remix Cloudflare Starter" },
    {
      name: "description",
      content:
        "Test and manage AI tools including text generation, streaming, and image generation.",
    },
  ];
};

export async function loader({}: LoaderFunctionArgs) {
  try {
    // Get available AI providers
    const providers = getAvailableProviders();

    // Get user credits (if authenticated)
    let userCredits = 0;
    try {
      const userUuid = await getUserUuid();
      if (userUuid) {
        const db = createDbFromEnv();
        userCredits = await getUserCredits(userUuid, db);
      }
    } catch (error) {
      console.warn("Failed to get user credits:", error);
    }

    return json({
      providers,
      userCredits,
      env: {
        hasOpenAI: !!context.cloudflare?.env?.OPENAI_API_KEY,
        hasDeepSeek: !!context.cloudflare?.env?.DEEPSEEK_API_KEY,
        hasOpenRouter: !!context.cloudflare?.env?.OPENROUTER_API_KEY,
        hasSiliconFlow: !!context.cloudflare?.env?.SILICONFLOW_API_KEY,
        hasReplicate: !!context.cloudflare?.env?.REPLICATE_API_TOKEN,
      },
    });
  } catch (error) {
    console.error("Failed to load AI tools data:", error);
    return json({
      providers: [],
      userCredits: 0,
      env: {},
    });
  }
}

export default function AIToolsPage() {
  const { providers, userCredits, env } = useLoaderData<typeof loader>();

  // Text generation state
  const [textPrompt, setTextPrompt] = useState("");
  const [textProvider, setTextProvider] = useState<AIProvider>("openai");
  const [textModel, setTextModel] = useState("");
  const [textResult, setTextResult] = useState("");
  const [textLoading, setTextLoading] = useState(false);

  // Stream text state
  const [streamPrompt, setStreamPrompt] = useState("");
  const [streamProvider, setStreamProvider] = useState<AIProvider>("openai");
  const [streamModel, setStreamModel] = useState("");
  const [streamResult, setStreamResult] = useState("");
  const [streamLoading, setStreamLoading] = useState(false);

  // Image generation state
  const [imagePrompt, setImagePrompt] = useState("");
  const [imageProvider, setImageProvider] = useState<"openai" | "replicate">("openai");
  const [imageModel, setImageModel] = useState("dall-e-3");
  const [imageSize, setImageSize] = useState("1024x1024");
  const [imageResults, setImageResults] = useState<string[]>([]);
  const [imageLoading, setImageLoading] = useState(false);

  // Get available models for selected provider
  const getModelsForProvider = (provider: AIProvider) => {
    const providerConfig = providers.find((p) => p.name === provider);
    return providerConfig?.models || [];
  };

  // Check if provider is available (has API key)
  const isProviderAvailable = (provider: AIProvider) => {
    switch (provider) {
      case "openai":
        return env.hasOpenAI;
      case "deepseek":
        return env.hasDeepSeek;
      case "openrouter":
        return env.hasOpenRouter;
      case "siliconflow":
        return env.hasSiliconFlow;
      case "replicate":
        return env.hasReplicate;
      default:
        return false;
    }
  };

  // Generate text
  const handleGenerateText = async () => {
    if (!textPrompt.trim() || !textProvider || !textModel) {
      toast.error("Please fill in all required fields");
      return;
    }

    setTextLoading(true);
    setTextResult("");

    try {
      const response = await fetch("/api/ai/generate-text", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          prompt: textPrompt,
          provider: textProvider,
          model: textModel,
        }),
      });

      const data = await response.json();

      if (data.code === 0) {
        setTextResult(data.data.text);
        toast.success("Text generated successfully!");
      } else {
        toast.error(data.message || "Failed to generate text");
      }
    } catch (error) {
      console.error("Text generation error:", error);
      toast.error("Failed to generate text");
    } finally {
      setTextLoading(false);
    }
  };

  // Stream text
  const handleStreamText = async () => {
    if (!streamPrompt.trim() || !streamProvider || !streamModel) {
      toast.error("Please fill in all required fields");
      return;
    }

    setStreamLoading(true);
    setStreamResult("");

    try {
      const response = await fetch("/api/ai/stream-text", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          prompt: streamPrompt,
          provider: streamProvider,
          model: streamModel,
        }),
      });

      if (!response.ok) {
        throw new Error("Stream request failed");
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("No response body");
      }

      const decoder = new TextDecoder();
      let result = "";

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        result += chunk;
        setStreamResult(result);
      }

      toast.success("Text streaming completed!");
    } catch (error) {
      console.error("Text streaming error:", error);
      toast.error("Failed to stream text");
    } finally {
      setStreamLoading(false);
    }
  };

  // Generate image
  const handleGenerateImage = async () => {
    if (!imagePrompt.trim() || !imageProvider || !imageModel) {
      toast.error("Please fill in all required fields");
      return;
    }

    setImageLoading(true);
    setImageResults([]);

    try {
      const response = await fetch("/api/ai/generate-image", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          prompt: imagePrompt,
          provider: imageProvider,
          model: imageModel,
          size: imageSize,
        }),
      });

      const data = await response.json();

      if (data.code === 0) {
        setImageResults(data.data.images);
        toast.success("Image generated successfully!");
      } else {
        toast.error(data.message || "Failed to generate image");
      }
    } catch (error) {
      console.error("Image generation error:", error);
      toast.error("Failed to generate image");
    } finally {
      setImageLoading(false);
    }
  };

  return (
    <UnifiedLayout
      hero={{
        title: "AI Tools Showcase",
        description:
          "A collection of AI utilities including chat, image generation, and text analysis.",
      }}
    >
      <section className="py-16">
        <div className="max-w-6xl mx-auto px-4">
          {/* Provider Status */}
          <div className="mb-8 flex flex-wrap gap-2">
            {providers.map((provider) => (
              <Badge
                key={provider.name}
                variant={isProviderAvailable(provider.name as AIProvider) ? "default" : "outline"}
                className="text-xs"
              >
                {provider.displayName}
                {!isProviderAvailable(provider.name as AIProvider) && " (No API Key)"}
              </Badge>
            ))}
          </div>

          <Tabs defaultValue="text" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="text" className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                Text Generation
              </TabsTrigger>
              <TabsTrigger value="stream" className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Stream Text
              </TabsTrigger>
              <TabsTrigger value="image" className="flex items-center gap-2">
                <Image className="h-4 w-4" />
                Image Generation
              </TabsTrigger>
            </TabsList>

            {/* Text Generation Tab */}
            <TabsContent value="text">
              <Card>
                <CardHeader>
                  <CardTitle>Text Generation</CardTitle>
                  <CardDescription>
                    Generate text using various AI models. Cost: 5 credits per generation.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="text-provider">Provider</Label>
                      <Select
                        value={textProvider}
                        onValueChange={(value) => {
                          setTextProvider(value as AIProvider);
                          setTextModel("");
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select provider" />
                        </SelectTrigger>
                        <SelectContent>
                          {providers.map((provider) => (
                            <SelectItem
                              key={provider.name}
                              value={provider.name}
                              disabled={!isProviderAvailable(provider.name as AIProvider)}
                            >
                              {provider.displayName}
                              {!isProviderAvailable(provider.name as AIProvider) && " (No API Key)"}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="text-model">Model</Label>
                      <Select value={textModel} onValueChange={setTextModel}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select model" />
                        </SelectTrigger>
                        <SelectContent>
                          {getModelsForProvider(textProvider).map((model) => (
                            <SelectItem key={model} value={model}>
                              {model}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="text-prompt">Prompt</Label>
                    <Textarea
                      id="text-prompt"
                      placeholder="Enter your prompt here..."
                      value={textPrompt}
                      onChange={(e) => setTextPrompt(e.target.value)}
                      rows={4}
                    />
                  </div>
                  <Button
                    onClick={handleGenerateText}
                    disabled={textLoading || !isProviderAvailable(textProvider)}
                    className="w-full"
                  >
                    {textLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      "Generate Text"
                    )}
                  </Button>
                  {textResult && (
                    <div className="space-y-2">
                      <Label>Result</Label>
                      <div className="p-4 bg-muted rounded-lg whitespace-pre-wrap">
                        {textResult}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Stream Text Tab */}
            <TabsContent value="stream">
              <Card>
                <CardHeader>
                  <CardTitle>Stream Text Generation</CardTitle>
                  <CardDescription>
                    Generate text with real-time streaming. Cost: 3 credits per generation.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="stream-provider">Provider</Label>
                      <Select
                        value={streamProvider}
                        onValueChange={(value) => {
                          setStreamProvider(value as AIProvider);
                          setStreamModel("");
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select provider" />
                        </SelectTrigger>
                        <SelectContent>
                          {providers.map((provider) => (
                            <SelectItem
                              key={provider.name}
                              value={provider.name}
                              disabled={!isProviderAvailable(provider.name as AIProvider)}
                            >
                              {provider.displayName}
                              {!isProviderAvailable(provider.name as AIProvider) && " (No API Key)"}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="stream-model">Model</Label>
                      <Select value={streamModel} onValueChange={setStreamModel}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select model" />
                        </SelectTrigger>
                        <SelectContent>
                          {getModelsForProvider(streamProvider).map((model) => (
                            <SelectItem key={model} value={model}>
                              {model}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="stream-prompt">Prompt</Label>
                    <Textarea
                      id="stream-prompt"
                      placeholder="Enter your prompt here..."
                      value={streamPrompt}
                      onChange={(e) => setStreamPrompt(e.target.value)}
                      rows={4}
                    />
                  </div>
                  <Button
                    onClick={handleStreamText}
                    disabled={streamLoading || !isProviderAvailable(streamProvider)}
                    className="w-full"
                  >
                    {streamLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Streaming...
                      </>
                    ) : (
                      "Stream Text"
                    )}
                  </Button>
                  {streamResult && (
                    <div className="space-y-2">
                      <Label>Streaming Result</Label>
                      <div className="p-4 bg-muted rounded-lg whitespace-pre-wrap min-h-[200px]">
                        {streamResult}
                        {streamLoading && <span className="animate-pulse">▋</span>}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Image Generation Tab */}
            <TabsContent value="image">
              <Card>
                <CardHeader>
                  <CardTitle>Image Generation</CardTitle>
                  <CardDescription>
                    Generate images using AI models. Cost: 10 credits per image.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="image-provider">Provider</Label>
                      <Select
                        value={imageProvider}
                        onValueChange={(value) => {
                          setImageProvider(value as "openai" | "replicate");
                          setImageModel(
                            value === "openai" ? "dall-e-3" : "stability-ai/stable-diffusion"
                          );
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select provider" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="openai" disabled={!env.hasOpenAI}>
                            OpenAI {!env.hasOpenAI && "(No API Key)"}
                          </SelectItem>
                          <SelectItem value="replicate" disabled={!env.hasReplicate}>
                            Replicate {!env.hasReplicate && "(No API Key)"}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="image-model">Model</Label>
                      <Select value={imageModel} onValueChange={setImageModel}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select model" />
                        </SelectTrigger>
                        <SelectContent>
                          {imageProvider === "openai" ? (
                            <>
                              <SelectItem value="dall-e-2">DALL-E 2</SelectItem>
                              <SelectItem value="dall-e-3">DALL-E 3</SelectItem>
                            </>
                          ) : (
                            <SelectItem value="stability-ai/stable-diffusion">
                              Stable Diffusion
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="image-size">Size</Label>
                      <Select value={imageSize} onValueChange={setImageSize}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select size" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="256x256">256x256</SelectItem>
                          <SelectItem value="512x512">512x512</SelectItem>
                          <SelectItem value="1024x1024">1024x1024</SelectItem>
                          <SelectItem value="1792x1024">1792x1024</SelectItem>
                          <SelectItem value="1024x1792">1024x1792</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="image-prompt">Prompt</Label>
                    <Textarea
                      id="image-prompt"
                      placeholder="Describe the image you want to generate..."
                      value={imagePrompt}
                      onChange={(e) => setImagePrompt(e.target.value)}
                      rows={3}
                    />
                  </div>
                  <Button
                    onClick={handleGenerateImage}
                    disabled={
                      imageLoading ||
                      (imageProvider === "openai" && !env.hasOpenAI) ||
                      (imageProvider === "replicate" && !env.hasReplicate)
                    }
                    className="w-full"
                  >
                    {imageLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      "Generate Image"
                    )}
                  </Button>
                  {imageResults.length > 0 && (
                    <div className="space-y-2">
                      <Label>Generated Images</Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {imageResults.map((imageUrl, index) => (
                          <div key={index} className="border rounded-lg overflow-hidden">
                            <img
                              src={imageUrl}
                              alt={`Generated image ${index + 1}`}
                              className="w-full h-auto"
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </section>
    </UnifiedLayout>
  );
}
