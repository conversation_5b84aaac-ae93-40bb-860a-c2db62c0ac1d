/**
 * Dynamic sitemap.xml generation
 */

import type { LoaderFunctionArgs } from "@remix-run/node";
import { siteConfig } from "~/config/seo"; // Updated import path

export async function loader({ request }: LoaderFunctionArgs) {
  const baseUrl = siteConfig.url.replace(/\/$/, ""); // Remove trailing slash

  // Define your site's pages
  const pages = [
    {
      url: "/",
      changefreq: "daily",
      priority: "1.0",
      lastmod: new Date().toISOString(),
    },
    {
      url: "/components",
      changefreq: "weekly",
      priority: "0.8",
      lastmod: new Date().toISOString(),
    },
    {
      url: "/zustand",
      changefreq: "monthly",
      priority: "0.6",
      lastmod: new Date().toISOString(),
    },
    // Add more pages as needed
  ];

  // Generate sitemap XML (simplified, no i18n)
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${pages
  .map((page) => {
    const url = `${baseUrl}${page.url}`;

    return `  <url>
    <loc>${url}</loc>
    <lastmod>${page.lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`;
  })
  .join("\n")}
</urlset>`;

  return new Response(sitemap, {
    status: 200,
    headers: {
      "Content-Type": "application/xml",
      "Cache-Control": "public, max-age=3600", // Cache for 1 hour
    },
  });
}
