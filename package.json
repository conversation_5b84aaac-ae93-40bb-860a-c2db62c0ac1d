{"name": "remix-vercel-neon-starter", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "clean": "rm -rf build", "clean:modules": "npx rimraf node_modules", "deploy": "vercel --prod", "dev": "remix vite:dev", "dev:https": "cross-env HTTPS=true remix vite:dev", "dev:http": "remix vite:dev", "setup:https": "tsx scripts/setup-https.ts", "lint": "biome lint .", "lint:fix": "biome lint --write .", "format": "biome format .", "format:fix": "biome format --write .", "check": "biome check .", "check:fix": "biome check --write .", "test": "vitest run", "start": "remix-serve ./build/server/index.js", "typecheck": "tsc", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "build:analyze": "ANALYZE=true remix vite:build", "analyze": "pnpm run build:analyze", "perf": "pnpm run build && pnpm run start"}, "dependencies": {"@ai-sdk/deepseek": "^0.2.14", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/openai-compatible": "^0.2.14", "@ai-sdk/provider": "^1.1.3", "@ai-sdk/provider-utils": "^2.2.8", "@ai-sdk/replicate": "^0.2.8", "@neondatabase/serverless": "^1.0.1", "@openrouter/ai-sdk-provider": "^0.7.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@react-email/render": "^1.1.2", "@remix-run/node": "^2.16.8", "@remix-run/react": "^2.16.8", "@remix-run/serve": "^2.16.8", "@remix-run/server-runtime": "^2.16.8", "@vercel/blob": "^0.27.0", "@stackframe/react": "^2.8.12", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookie": "^1.0.2", "drizzle-orm": "^0.44.2", "google-one-tap": "^1.0.6", "isbot": "^5.1.28", "lucide-react": "^0.522.0", "marked": "^15.0.12", "openai": "^5.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "resend": "^4.5.2", "sonner": "^2.0.5", "stripe": "^18.2.1", "tailwind-merge": "^3.3.1", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@biomejs/biome": "^2.0.4", "@remix-run/dev": "^2.16.8", "@types/node": "^22.10.5", "@tailwindcss/postcss": "^4.1.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "drizzle-kit": "^0.31.1", "postcss": "^8.5.6", "rimraf": "^6.0.1", "shadcn": "^2.7.0", "tailwindcss": "^4.1.10", "tsx": "^4.20.3", "typescript": "^5.8.3", "vercel": "^39.2.4", "vite": "^6.3.5", "vite-bundle-analyzer": "^0.22.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "engines": {"node": ">=20.0.0"}, "packageManager": "pnpm@9.0.0"}