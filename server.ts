import { createRe<PERSON><PERSON><PERSON><PERSON> } from "@remix-run/node";
import type { ServerBuild } from "@remix-run/node";
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore This file won't exist if it hasn't yet been built
import * as build from "./build/server";
import { getLoadContext } from "./load-context";

const handleRemixRequest = createRequestHandler(build as ServerBuild, "production");

export default async function handler(request: Request): Promise<Response> {
  try {
    const loadContext = getLoadContext();
    return await handleRemixRequest(request, loadContext);
  } catch (error) {
    console.log(error);
    return new Response("An unexpected error occurred", { status: 500 });
  }
}
