{"buildCommand": "pnpm run build", "devCommand": "pnpm run dev", "installCommand": "pnpm install", "framework": "remix", "functions": {"app/routes/**/*.ts": {"maxDuration": 30}, "app/routes/**/*.tsx": {"maxDuration": 30}}, "env": {"NODE_ENV": "production"}, "regions": ["iad1"], "rewrites": [{"source": "/build/(.*)", "destination": "/build/$1"}], "headers": [{"source": "/build/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}