/// <reference types="vitest" />

import { vitePlugin as remix } from "@remix-run/dev";
import { existsSync, readFileSync } from "fs";
import path from "path";
import { defineConfig } from "vite";
import { analyzer } from "vite-bundle-analyzer";
import tsconfigPaths from "vite-tsconfig-paths";

declare module "@remix-run/dev" {
  interface Future {
    v3_singleFetch: false;
  }
}

export default defineConfig({
  define: {
    global: "globalThis",
  },
  // Development server configuration
  server: (() => {
    const baseConfig = {
      port: process.env.PORT ? parseInt(process.env.PORT) : 5173,
      strictPort: false, // 如果端口被占用，不要自动尝试其他端口
      host: true, // 允许外部访问
      http2: false, // 禁用 HTTP/2 以避免头部处理问题
      force: true, // 强制重新构建依赖
    };

    // HTTPS 配置 - 当环境变量 HTTPS=true 时启用
    if (process.env.HTTPS === "true") {
      const certPath = path.join(__dirname, ".certs/cert.pem");
      const keyPath = path.join(__dirname, ".certs/key.pem");

      if (existsSync(certPath) && existsSync(keyPath)) {
        return {
          ...baseConfig,
          https: {
            key: readFileSync(keyPath),
            cert: readFileSync(certPath),
          },
        };
      } else {
        console.warn("⚠️  HTTPS 证书文件不存在，请运行: pnpm run setup:https");
        return {
          ...baseConfig,
          https: true as any, // 使用 Vite 的自动生成证书
        };
      }
    }

    return {
      ...baseConfig,
      // 添加一些配置来避免头部处理问题
      hmr: {
        overlay: false, // 禁用错误覆盖层
      },
      middlewareMode: false, // 确保不使用中间件模式
      proxy: {}, // 明确设置空代理配置
    };
  })(),
  // Exclude .000 directory from all processing
  resolve: {
    alias: {
      "~": path.resolve(__dirname, "./app"),
    },
    mainFields: ["browser", "module", "main"],
  },
  plugins: [
    remix({
      future: {
        v3_fetcherPersist: true,
        v3_relativeSplatPath: true,
        v3_throwAbortReason: true,
        v3_singleFetch: false,
        v3_lazyRouteDiscovery: true,
      },
      // Exclude .000 directory from Remix processing
      ignoredRouteFiles: [
        "**/.*",
        "**/*.css",
        "**/*.test.{js,jsx,ts,tsx}",
        ".000/**/*",
        "**/*.server.{ts,tsx}",
      ],
    }),
    tsconfigPaths({
      // Exclude .000 directory from TypeScript path resolution
      projects: ["./tsconfig.json"],
      ignoreConfigErrors: true,
    }),
    // Bundle analyzer - only when ANALYZE=true
    ...(process.env.ANALYZE === "true"
      ? [analyzer({ analyzerMode: "static", openAnalyzer: true })]
      : []),
  ],
  ssr: {
    noExternal: ["@stackframe/react"],
    external: ["react-dom/server"],
  },
  optimizeDeps: {
    include: ["@stackframe/react"],
    exclude: [],
  },
  build: {
    minify: true,
    // Enhanced build configuration for performance and smaller bundles
    rollupOptions: {
      output: {
        // Manual chunk splitting for better caching and smaller bundles
        manualChunks: (id) => {
          // Only apply manual chunks for client build
          if (id.includes("node_modules")) {
            // Core React libraries
            if (id.includes("react") || id.includes("react-dom")) {
              return "vendor";
            }
            // UI components
            if (id.includes("@radix-ui")) {
              return "ui";
            }
            // Utility libraries
            if (
              id.includes("clsx") ||
              id.includes("tailwind-merge") ||
              id.includes("class-variance-authority")
            ) {
              return "utils";
            }
            // AI SDKs - separate chunk for better caching
            if (id.includes("@ai-sdk") || id.includes("openai") || id.includes("ai/")) {
              return "ai";
            }
            // Keystatic CMS - separate chunk
            if (id.includes("@keystatic")) {
              return "keystatic";
            }
            // Icons
            if (id.includes("lucide-react")) {
              return "icons";
            }
            // Payment libraries
            if (id.includes("stripe")) {
              return "payments";
            }
            // Other large libraries
            if (id.includes("zod") || id.includes("drizzle-orm")) {
              return "validation";
            }
          }
        },
      },
      // External dependencies for Node.js
      external: [],
    },
    // Disable source maps in production for smaller bundles
    sourcemap: false,
    // Chunk size warning limit
    chunkSizeWarningLimit: 500,
  },
});
